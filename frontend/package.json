{"name": "simbios", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "typecheck": "tsc -b", "lint": "eslint src/ --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src/ --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --ignore-unknown --check .", "format:fix": "prettier --ignore-unknown --write .", "prepare": "cd .. && husky frontend/.husky"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@sentry/react": "^10.5.0", "@tailwindcss/vite": "^4.1.12", "@tanstack/react-query": "^5.85.5", "@tanstack/react-query-devtools": "^5.85.5", "@tanstack/react-router": "^1.131.27", "@tanstack/react-router-devtools": "^1.131.27", "@tanstack/router-plugin": "^1.131.27", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.541.0", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12", "zustand": "^5.0.8"}, "devDependencies": {"@eslint-react/eslint-plugin": "^1.52.6", "@eslint/js": "^9.33.0", "@tanstack/eslint-plugin-query": "^5.83.1", "@tanstack/eslint-plugin-router": "^1.131.2", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.8", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-formatter-gitlab": "^6.0.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-unicorn": "^60.0.0", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "prettier": "^3.6.2", "prettier-plugin-multiline-arrays": "^4.0.3", "tw-animate-css": "^1.3.7", "typescript": "~5.9.2", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}, "lint-staged": {"!*.{js,jsx,ts,tsx}": "prettier --ignore-unknown --write", "*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}}
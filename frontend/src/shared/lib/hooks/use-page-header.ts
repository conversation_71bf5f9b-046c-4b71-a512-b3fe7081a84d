import { useEffect } from 'react'
import { useHeader, type HeaderConfig } from '@/shared/lib/contexts/header-context'

/**
 * Хук для настройки хедера страницы
 * Автоматически сбрасывает конфигурацию при размонтировании компонента
 */
export function usePageHeader(config: HeaderConfig) {
    const { setConfig, resetConfig } = useHeader()

    useEffect(() => {
        setConfig(config)

        // Сброс конфигурации при размонтировании
        return () => {
            resetConfig()
        }
    }, [config, setConfig, resetConfig])
}

/**
 * Хук для динамического обновления хедера
 * Не сбрасывает конфигурацию автоматически
 */
export function useDynamicHeader() {
    const { config, updateConfig } = useHeader()

    return {
        config,
        updateConfig,
    }
}

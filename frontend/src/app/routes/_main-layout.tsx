import { createFileRoute, Outlet } from '@tanstack/react-router'
import React from 'react'
import { HeaderSlotContext } from '@/services/header'

export const Route = createFileRoute('/_main-layout')({
    component: AppLayout,
})

function AppLayout() {
    const [headerSlot, setHeaderSlot] = React.useState<HTMLElement | null>(null)

    const headerSlotRef = React.useCallback((el: HTMLElement | null) => {
        setHeaderSlot(el)
    }, [])

    return (
        <div className="flex flex-row h-screen">
            <p>Sidebar</p>
            <div className="flex flex-col w-full grow">
                <main className="flex-1 overflow-auto p-6 space-y-6">
                    <div
                        ref={headerSlotRef}
                        id="header-slot"
                    />

                    <HeaderSlotContext value={headerSlot}>
                        <div className="w-full flex-1 rounded-lg border bg-card p-6">
                            <Outlet />
                        </div>
                    </HeaderSlotContext>
                </main>
            </div>
        </div>
    )
}

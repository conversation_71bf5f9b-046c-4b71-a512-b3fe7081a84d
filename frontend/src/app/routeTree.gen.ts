/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as MainLayoutRouteImport } from './routes/_main-layout'
import { Route as MainLayoutIndexRouteImport } from './routes/_main-layout/index'

const MainLayoutRoute = MainLayoutRouteImport.update({
  id: '/_main-layout',
  getParentRoute: () => rootRouteImport,
} as any)
const MainLayoutIndexRoute = MainLayoutIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => MainLayoutRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof MainLayoutIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof MainLayoutIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_main-layout': typeof MainLayoutRouteWithChildren
  '/_main-layout/': typeof MainLayoutIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/'
  fileRoutesByTo: FileRoutesByTo
  to: '/'
  id: '__root__' | '/_main-layout' | '/_main-layout/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  MainLayoutRoute: typeof MainLayoutRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_main-layout': {
      id: '/_main-layout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof MainLayoutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_main-layout/': {
      id: '/_main-layout/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof MainLayoutIndexRouteImport
      parentRoute: typeof MainLayoutRoute
    }
  }
}

interface MainLayoutRouteChildren {
  MainLayoutIndexRoute: typeof MainLayoutIndexRoute
}

const MainLayoutRouteChildren: MainLayoutRouteChildren = {
  MainLayoutIndexRoute: MainLayoutIndexRoute,
}

const MainLayoutRouteWithChildren = MainLayoutRoute._addFileChildren(
  MainLayoutRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  MainLayoutRoute: MainLayoutRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
